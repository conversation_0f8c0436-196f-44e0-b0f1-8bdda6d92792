{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:pages": "vite build && cp _headers _redirects dist/ && cp public/_redirects dist/_redirects", "lint": "eslint .", "preview": "vite preview", "pages:dev": "wrangler pages dev dist --compatibility-date=2024-01-15", "pages:deploy": "npm run build:pages && wrangler pages deploy dist"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.1.1", "@mux/mux-player": "^3.4.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^2.7.0", "@silvermine/videojs-quality-selector": "^1.3.1", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.56.2", "@types/hls.js": "^1.0.0", "@types/stripe": "^8.0.417", "@types/video-react": "^0.15.8", "@videojs/http-streaming": "^3.15.0", "antd": "^5.24.9", "caniuse-lite": "^1.0.30001723", "class-variance-authority": "^0.7.1", "cloudinary-react": "^1.8.1", "cloudinary-video-player": "^2.5.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.18.1", "hls.js": "^1.6.2", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "localforage": "^1.10.0", "lucide-react": "^0.515.0", "match-sorter": "^8.0.3", "moment": "^2.30.1", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.0", "react-icons": "^5.5.0", "react-photo-view": "^1.2.7", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.3", "react-responsive": "^10.0.1", "react-router": "6.30.1", "react-router-dom": "6.30.1", "react-video-audio-player": "^1.3.4", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "screenfull": "^6.0.2", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "sort-by": "^1.2.0", "stripe": "^18.1.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "video-react": "^0.16.0", "video.js": "^8.22.0", "zod": "^3.25.64"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/lodash": "^4.17.17", "@types/node": "^22.5.5", "@types/react": "^18.3.12", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.1", "@types/redux-persist": "^4.3.1", "@types/socket.io-client": "^3.0.0", "@types/video.js": "^7.3.58", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-remove-console": "^2.2.0"}}