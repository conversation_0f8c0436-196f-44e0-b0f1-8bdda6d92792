import { useState, useEffect, useMemo } from "react";
import { 
  Star, 
  MessageSquare, 
  TrendingUp, 
  Download, 
  RefreshCw,
  Grid3X3,
  List,
  BarChart3
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

// Redux
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useGetMeQuery } from "@/redux/features/auth/authApi";
import { useGetCreatorCourseQuery } from "@/redux/features/course/courseApi";
import {
  useGetTeacherReviewsQuery,
  useGetReviewAnalyticsQuery,
  useGetReviewStatsQuery,
  useExportReviewsMutation
} from "@/redux/features/review/reviewApi";
import {
  setFilters,
  setViewMode,
  setActiveTab,
  setSearch,
  nextPage,
  clearSelectedReviews
} from "@/redux/features/review/reviewSlice";

// Components
import ReviewStatsOverview from "@/components/Reviews/ReviewStatsOverview";
import ReviewList from "@/components/Reviews/ReviewList";
import ReviewFilters from "@/components/Reviews/ReviewFilters";
import ReviewSearch from "@/components/Reviews/ReviewSearch";
import ReviewSort from "@/components/Reviews/ReviewSort";
import ReviewTrendChart from "@/components/Reviews/ReviewTrendChart";
import ReviewErrorBoundary from "@/components/Reviews/ReviewErrorBoundary";
import {
  ReviewPageLoadingSkeleton,
  ReviewErrorState
} from "@/components/Reviews/ReviewLoadingStates";

// Types
import { ReviewFilters as IReviewFilters } from "@/types/review";

const Reviews: React.FC = () => {
  const dispatch = useAppDispatch();
  const { data: userData } = useGetMeQuery(undefined);
  const teacherId = userData?.data?._id;

  // Local state
  const [refreshing, setRefreshing] = useState(false);

  // Redux state
  const reviewState = useAppSelector((state) => state.review);
  const { filters, viewMode, ui } = reviewState;

  // API queries
  const { 
    data: reviewsData, 
    isLoading: reviewsLoading, 
    error: reviewsError,
    refetch: refetchReviews
  } = useGetTeacherReviewsQuery(
    { teacherId: teacherId!, filters },
    { skip: !teacherId }
  );

  const { 
    data: statsData, 
    isLoading: statsLoading,
    refetch: refetchStats
  } = useGetReviewStatsQuery(
    { teacherId: teacherId! },
    { skip: !teacherId }
  );

  const { 
    data: analyticsData, 
    isLoading: analyticsLoading,
    refetch: refetchAnalytics
  } = useGetReviewAnalyticsQuery(
    { teacherId: teacherId!, timeRange: 'month' },
    { skip: !teacherId }
  );

  const { 
    data: coursesData,
    isLoading: coursesLoading
  } = useGetCreatorCourseQuery(teacherId!, { skip: !teacherId });

  // Mutations
  const [exportReviews, { isLoading: exportLoading }] = useExportReviewsMutation();

  // Memoized data
  const courses = useMemo(() => {
    return coursesData?.data?.courses || [];
  }, [coursesData]);

  const reviews = useMemo(() => {
    return reviewsData?.reviews || [];
  }, [reviewsData]);

  const stats = useMemo(() => {
    return statsData || {
      totalReviews: 0,
      averageRating: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      recentReviews: 0,
      responseRate: 0,
      sentimentScore: 0,
      monthlyGrowth: 0,
      weeklyGrowth: 0
    };
  }, [statsData]);

  const analytics = useMemo(() => {
    return analyticsData || {
      stats,
      trends: [],
      topCourses: [],
      recentActivity: [],
      ratingTrends: []
    };
  }, [analyticsData, stats]);

  // Handlers
  const handleFiltersChange = (newFilters: Partial<IReviewFilters>) => {
    dispatch(setFilters(newFilters));
  };

  const handleSearchChange = (search: string) => {
    dispatch(setSearch(search));
  };

  const handleSortChange = (
    sortBy: IReviewFilters['sortBy'], 
    sortOrder: IReviewFilters['sortOrder']
  ) => {
    dispatch(setFilters({ sortBy, sortOrder }));
  };

  const handleViewModeChange = (mode: 'list' | 'grid' | 'compact') => {
    dispatch(setViewMode(mode));
  };

  const handleTabChange = (tab: 'overview' | 'reviews' | 'analytics' | 'insights') => {
    dispatch(setActiveTab(tab));
  };

  const handleLoadMore = () => {
    dispatch(nextPage());
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refetchReviews(),
        refetchStats(),
        refetchAnalytics()
      ]);
      toast.success("Reviews refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh reviews");
    } finally {
      setRefreshing(false);
    }
  };

  const handleExport = async () => {
    if (!teacherId) return;
    
    try {
      const result = await exportReviews({
        teacherId,
        options: {
          format: 'xlsx',
          filters,
          includeAnalytics: true
        }
      }).unwrap();
      
      // Create download link
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = `reviews-export-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success("Reviews exported successfully");
    } catch (error) {
      toast.error("Failed to export reviews");
    }
  };

  const handleRespond = (reviewId: string) => {
    // TODO: Implement review response modal
    toast.info("Review response feature coming soon");
  };

  // Loading state
  const isLoading = reviewsLoading || statsLoading || analyticsLoading;

  // Error handling
  useEffect(() => {
    if (reviewsError) {
      toast.error("Failed to load reviews");
    }
  }, [reviewsError]);

  // Clear selections on unmount
  useEffect(() => {
    return () => {
      dispatch(clearSelectedReviews());
    };
  }, [dispatch]);

  if (!teacherId) {
    return <ReviewPageLoadingSkeleton />;
  }

  if (reviewsError) {
    return (
      <ReviewErrorState
        title="Failed to load reviews"
        message="There was an error loading your reviews. Please check your connection and try again."
        onRetry={handleRefresh}
      />
    );
  }

  return (
    <ReviewErrorBoundary>
      <div
        className="space-y-6"
        role="main"
        aria-label="Reviews dashboard"
      >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reviews</h1>
          <p className="text-gray-600 mt-1">
            Manage and respond to student feedback
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={cn("w-4 h-4 mr-2", refreshing && "animate-spin")} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={exportLoading}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={ui.activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="reviews" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Reviews
            {stats.totalReviews > 0 && (
              <Badge variant="secondary" className="ml-1">
                {stats.totalReviews}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Star className="w-4 h-4" />
            Insights
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <ReviewStatsOverview 
            stats={stats} 
            isLoading={statsLoading}
          />
          
          {analytics.trends.length > 0 && (
            <ReviewTrendChart 
              data={analytics.trends}
              height={300}
            />
          )}
        </TabsContent>

        {/* Reviews Tab */}
        <TabsContent value="reviews" className="space-y-6">
          {/* Filters and Search */}
          <Card className="dashboard-card">
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center flex-1">
                  <ReviewSearch
                    value={filters.search || ''}
                    onChange={handleSearchChange}
                    className="w-full sm:w-80"
                  />
                  
                  <ReviewFilters
                    filters={filters}
                    onFiltersChange={handleFiltersChange}
                    courses={courses}
                    isLoading={coursesLoading}
                  />
                  
                  <ReviewSort
                    sortBy={filters.sortBy}
                    sortOrder={filters.sortOrder}
                    onSortChange={handleSortChange}
                  />
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleViewModeChange('list')}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleViewModeChange('grid')}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reviews List */}
          <ReviewList
            reviews={reviews}
            isLoading={reviewsLoading}
            onLoadMore={handleLoadMore}
            hasMore={reviewsData?.pagination?.hasNextPage || false}
            showCourse={true}
            showActions={true}
            onRespond={handleRespond}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-12 text-gray-500">
            <TrendingUp className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Advanced Analytics
            </h3>
            <p>Detailed analytics features coming soon</p>
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="text-center py-12 text-gray-500">
            <Star className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              AI-Powered Insights
            </h3>
            <p>Smart insights and recommendations coming soon</p>
          </div>
        </TabsContent>
      </Tabs>
      </div>
    </ReviewErrorBoundary>
  );
};

export default Reviews;
