import { Request, Response } from 'express';
import httpStatus from 'http-status';
import catchAsync from '../../utils/catchAsync';
import sendResponse from '../../utils/sendResponse';
import { StripeConnectService } from './stripeConnect.service';
import AppError from '../../errors/AppError';

// Create Stripe Connect account
const createAccount = catchAsync(async (req: Request, res: Response) => {
  const teacherId = req.user?.userId;
  const { type, country, email, business_type } = req.body;

  if (!teacherId) {
    throw new AppError(httpStatus.UNAUTHORIZED, 'Teacher ID not found');
  }

  if (!type || !country || !email) {
    throw new AppError(
      httpStatus.BAD_REQUEST,
      'Missing required fields: type, country, email'
    );
  }

  const result = await StripeConnectService.createStripeAccount(teacherId, {
    type,
    country,
    email,
    business_type,
  });

  sendResponse(res, {
    statusCode: httpStatus.CREATED,
    success: true,
    message: 'Stripe account created successfully',
    data: result,
  });
});

// Create account link for onboarding
const createAccountLink = catchAsync(async (req: Request, res: Response) => {
  const teacherId = req.user?.userId;
  const { type, refreshUrl, returnUrl } = req.body;

  if (!teacherId) {
    throw new AppError(httpStatus.UNAUTHORIZED, 'Teacher ID not found');
  }

  if (!type || !refreshUrl || !returnUrl) {
    throw new AppError(
      httpStatus.BAD_REQUEST,
      'Missing required fields: type, refreshUrl, returnUrl'
    );
  }

  const result = await StripeConnectService.createAccountLink(teacherId, {
    type,
    refreshUrl,
    returnUrl,
  });

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Account link created successfully',
    data: result,
  });
});

// Get account status
const getAccountStatus = catchAsync(async (req: Request, res: Response) => {
  const teacherId = req.user?.userId;

  if (!teacherId) {
    throw new AppError(httpStatus.UNAUTHORIZED, 'Teacher ID not found');
  }

  const result = await StripeConnectService.getAccountStatus(teacherId);

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Account status retrieved successfully',
    data: result,
  });
});

// Update account information
const updateAccount = catchAsync(async (req: Request, res: Response) => {
  const teacherId = req.user?.userId;
  const updateData = req.body;

  if (!teacherId) {
    throw new AppError(httpStatus.UNAUTHORIZED, 'Teacher ID not found');
  }

  const result = await StripeConnectService.updateAccount(teacherId, updateData);

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Account updated successfully',
    data: result,
  });
});

// Disconnect account
const disconnectAccount = catchAsync(async (req: Request, res: Response) => {
  const teacherId = req.user?.userId;

  if (!teacherId) {
    throw new AppError(httpStatus.UNAUTHORIZED, 'Teacher ID not found');
  }

  const result = await StripeConnectService.disconnectAccount(teacherId);

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Account disconnected successfully',
    data: result,
  });
});

export const StripeConnectController = {
  createAccount,
  createAccountLink,
  getAccountStatus,
  updateAccount,
  disconnectAccount,
};
