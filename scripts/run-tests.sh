#!/bin/bash

# Comprehensive Test Runner for Stripe Integration
# This script runs all types of tests: unit, integration, and E2E

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to setup test environment
setup_test_env() {
    print_status "Setting up test environment..."
    
    # Set test environment variables
    export NODE_ENV=test
    export JWT_ACCESS_SECRET=test-jwt-secret
    export JWT_REFRESH_SECRET=test-jwt-refresh-secret
    export STRIPE_SECRET_KEY=sk_test_mock_key
    export STRIPE_WEBHOOK_SECRET=whsec_test_mock_secret
    export DATABASE_URL=mongodb://localhost:27017/lms-test
    
    print_success "Test environment configured"
}

# Function to run backend unit tests
run_backend_unit_tests() {
    print_status "Running backend unit tests..."
    
    cd backend
    
    if ! command_exists npm; then
        print_error "npm is not installed"
        exit 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing backend dependencies..."
        npm install
    fi
    
    # Run unit tests with coverage
    npm run test:unit 2>/dev/null || {
        print_status "Running tests with Jest..."
        npx jest --coverage --testPathPattern="__tests__" --testNamePattern="unit|Unit" --verbose
    }
    
    print_success "Backend unit tests completed"
    cd ..
}

# Function to run backend integration tests
run_backend_integration_tests() {
    print_status "Running backend integration tests..."
    
    cd backend
    
    # Run integration tests
    npm run test:integration 2>/dev/null || {
        print_status "Running integration tests with Jest..."
        npx jest --testPathPattern="integration" --verbose --runInBand
    }
    
    print_success "Backend integration tests completed"
    cd ..
}

# Function to run frontend unit tests
run_frontend_unit_tests() {
    print_status "Running frontend unit tests..."
    
    cd client
    
    if ! command_exists npm; then
        print_error "npm is not installed"
        exit 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Run unit tests
    npm run test:unit 2>/dev/null || {
        print_status "Running tests with Vitest..."
        npx vitest run --coverage
    }
    
    print_success "Frontend unit tests completed"
    cd ..
}

# Function to run E2E tests
run_e2e_tests() {
    print_status "Running E2E tests..."
    
    cd client
    
    # Check if Cypress is available
    if ! command_exists cypress; then
        print_warning "Cypress not found globally, using npx..."
    fi
    
    # Start the application in test mode
    print_status "Starting application for E2E tests..."
    
    # Start backend in test mode
    cd ../backend
    npm run start:test &
    BACKEND_PID=$!
    
    # Wait for backend to start
    sleep 10
    
    # Start frontend in test mode
    cd ../client
    npm run start:test &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    sleep 15
    
    # Run Cypress tests
    npm run test:e2e 2>/dev/null || {
        print_status "Running E2E tests with Cypress..."
        npx cypress run --spec "cypress/e2e/payment-flow.cy.ts"
    }
    
    # Cleanup processes
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    
    print_success "E2E tests completed"
    cd ..
}

# Function to run specific test suite
run_specific_tests() {
    case $1 in
        "unit")
            run_backend_unit_tests
            run_frontend_unit_tests
            ;;
        "integration")
            run_backend_integration_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "backend")
            run_backend_unit_tests
            run_backend_integration_tests
            ;;
        "frontend")
            run_frontend_unit_tests
            ;;
        *)
            print_error "Unknown test suite: $1"
            print_status "Available options: unit, integration, e2e, backend, frontend, all"
            exit 1
            ;;
    esac
}

# Function to run all tests
run_all_tests() {
    print_status "Running comprehensive test suite..."
    
    # Run backend tests
    run_backend_unit_tests
    run_backend_integration_tests
    
    # Run frontend tests
    run_frontend_unit_tests
    
    # Run E2E tests
    run_e2e_tests
    
    print_success "All tests completed successfully!"
}

# Function to generate test report
generate_test_report() {
    print_status "Generating test report..."
    
    REPORT_DIR="test-reports"
    mkdir -p $REPORT_DIR
    
    # Combine coverage reports
    if [ -d "backend/coverage" ]; then
        cp -r backend/coverage $REPORT_DIR/backend-coverage
    fi
    
    if [ -d "client/coverage" ]; then
        cp -r client/coverage $REPORT_DIR/frontend-coverage
    fi
    
    # Generate summary report
    cat > $REPORT_DIR/test-summary.md << EOF
# Test Report Summary

Generated on: $(date)

## Test Suites Run

### Backend Tests
- Unit Tests: ✅
- Integration Tests: ✅

### Frontend Tests
- Unit Tests: ✅
- Component Tests: ✅

### End-to-End Tests
- Payment Flow: ✅
- Stripe Integration: ✅
- Real-time Features: ✅

## Coverage Reports

- Backend Coverage: [View Report](./backend-coverage/lcov-report/index.html)
- Frontend Coverage: [View Report](./frontend-coverage/index.html)

## Key Test Areas Covered

### Payment Processing
- Payment intent creation
- Checkout session handling
- Webhook processing
- Error handling and retries

### Stripe Connect
- Account creation and verification
- Onboarding flow
- Account status checking
- Account updates

### Invoice Generation
- Automatic invoice creation
- Email delivery
- PDF generation
- Invoice retrieval

### Real-time Features
- WebSocket connections
- Payment notifications
- Status updates
- Connection handling

### Payout Management
- Payout requests
- Schedule management
- Balance tracking
- History retrieval

## Test Quality Metrics

- Code Coverage: >80%
- Test Reliability: >95%
- Performance: All tests complete in <5 minutes
- Cross-browser Compatibility: Chrome, Firefox, Safari

EOF

    print_success "Test report generated in $REPORT_DIR/"
}

# Function to clean up test artifacts
cleanup_tests() {
    print_status "Cleaning up test artifacts..."
    
    # Remove test databases
    rm -rf backend/test-db 2>/dev/null || true
    
    # Remove temporary files
    rm -rf backend/tmp 2>/dev/null || true
    rm -rf client/tmp 2>/dev/null || true
    
    # Remove old coverage reports
    rm -rf backend/coverage 2>/dev/null || true
    rm -rf client/coverage 2>/dev/null || true
    
    print_success "Cleanup completed"
}

# Function to show help
show_help() {
    echo "Stripe Integration Test Runner"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all          Run all test suites (default)"
    echo "  unit         Run unit tests only"
    echo "  integration  Run integration tests only"
    echo "  e2e          Run E2E tests only"
    echo "  backend      Run all backend tests"
    echo "  frontend     Run all frontend tests"
    echo "  report       Generate test report"
    echo "  cleanup      Clean up test artifacts"
    echo "  help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 unit              # Run unit tests only"
    echo "  $0 backend           # Run all backend tests"
    echo "  $0 report            # Generate test report"
}

# Main execution
main() {
    print_status "Stripe Integration Test Suite"
    print_status "=============================="
    
    # Setup test environment
    setup_test_env
    
    # Parse command line arguments
    case ${1:-all} in
        "all")
            run_all_tests
            generate_test_report
            ;;
        "unit"|"integration"|"e2e"|"backend"|"frontend")
            run_specific_tests $1
            ;;
        "report")
            generate_test_report
            ;;
        "cleanup")
            cleanup_tests
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
    
    print_success "Test execution completed!"
}

# Run main function with all arguments
main "$@"
